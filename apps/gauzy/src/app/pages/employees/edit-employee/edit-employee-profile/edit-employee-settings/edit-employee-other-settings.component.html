<form class="main-form" #ngForm="ngForm" [formGroup]="form" (ngSubmit)="onSubmit(ngForm)" novalidate>
	<aside class="aside-nav">
		<h4 class="settings-section-header">
			{{ 'EMPLOYEES_PAGE.EDIT_EMPLOYEE.SETTINGS_SECTION' | translate }}
		</h4>
		<ul>
			<span (click)="general.toggle()">
				<li [ngClass]="{ active: general?.expanded }">
					{{ 'EMPLOYEES_PAGE.EDIT_EMPLOYEE.GENERAL_SETTINGS' | translate }}
				</li>
			</span>
			<span (click)="timer.toggle()">
				<li [ngClass]="{ active: timer?.expanded }">
					{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TIMER_SETTINGS' | translate }}
				</li>
			</span>
			<span (click)="integrations.toggle()">
				<li [ngClass]="{ active: integrations?.expanded }">
					{{ 'EMPLOYEES_PAGE.EDIT_EMPLOYEE.INTEGRATIONS' | translate }}
				</li>
			</span>
		</ul>
	</aside>
	<section class="fields-section">
		<div class="accordion-section">
			<nb-accordion #accordion>
				<nb-accordion-item #general [expanded]="true">
					<nb-accordion-item-header>
						{{ 'EMPLOYEES_PAGE.EDIT_EMPLOYEE.GENERAL_SETTINGS' | translate }}
					</nb-accordion-item-header>
					<nb-accordion-item-body>
						<div class="fields">
							<div class="row">
								<div class="col-6">
									<ga-timezone-selector formControlName="timeZone"></ga-timezone-selector>
								</div>
								<div class="col-6">
									<div class="form-group">
										<label class="label" for="timeZone">
											{{ 'FORM.LABELS.TIME_FORMAT' | translate }}
										</label>
										<ng-select
											id="timeFormat"
											appendTo="body"
											[(items)]="listOfTimeFormats"
											[placeholder]="'FORM.PLACEHOLDERS.TIME_FORMAT' | translate"
											[searchable]="false"
											[clearable]="false"
											formControlName="timeFormat"
										></ng-select>
									</div>
								</div>
							</div>
						</div>
					</nb-accordion-item-body>
				</nb-accordion-item>
				<nb-accordion-item #timer>
					<nb-accordion-item-header>
						{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TIMER_SETTINGS' | translate }}
					</nb-accordion-item-header>
					<nb-accordion-item-body>
						<div class="fields time-tracker">
							<div class="row">
								<div class="col-xl-6 col-12 pt-2">
									<div class="form-group invite-toggle">
										<nb-toggle
											class="d-block"
											formControlName="allowManualTime"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ALLOW_MANUAL_TIME' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.ALLOW_MANUAL_TIME_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
								<div class="col-xl-6 col-12 pt-2">
									<div class="form-group invite-toggle">
										<nb-toggle
											class="d-block"
											formControlName="allowModifyTime"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ALLOW_MODIFY_TIME' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.ALLOW_MODIFY_TIME_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
								<div class="col-xl-6 col-12 pt-2">
									<div class="form-group invite-toggle">
										<nb-toggle
											class="d-block"
											formControlName="allowDeleteTime"
											labelPosition="start"
											status="primary"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ALLOW_DELETE_TIME' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.ALLOW_DELETE_TIME_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>

								<div class="col-xl-6 col-12 pt-2 form-group invite-toggle">
									<nb-toggle
										class="d-block"
										formControlName="allowScreenshotCapture"
										status="primary"
										labelPosition="start"
									>
										{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ALLOW_SCREEN_CAPTURE' | translate }}
										<button
											[nbTooltip]="
												'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ALLOW_SCREEN_CAPTURE_INFO' | translate
											"
											ghost
											nbButton
											size="small"
											status="info"
											class="p-0"
										>
											<nb-icon icon="info"></nb-icon>
										</button>
									</nb-toggle>
								</div>
							</div>
						</div>
					</nb-accordion-item-body>
				</nb-accordion-item>
				<nb-accordion-item #integrations>
					<nb-accordion-item-header>
						{{ 'EMPLOYEES_PAGE.EDIT_EMPLOYEE.INTEGRATIONS' | translate }}
					</nb-accordion-item-header>
					<nb-accordion-item-body>
						<div class="fields">
							<div class="row">
								<div class="col-6">
									<div class="form-group">
										<label class="label">
											{{ 'FORM.LABELS.UPWORK_ID' | translate }}
										</label>
										<input
											[placeholder]="'FORM.PLACEHOLDERS.UPWORK_ID' | translate"
											formControlName="upworkId"
											fullWidth
											nbInput
										/>
									</div>
								</div>
								<div class="col-6">
									<div class="form-group">
										<label class="label">
											{{ 'FORM.LABELS.LINKEDIN_ID' | translate }}
										</label>
										<input
											[placeholder]="'FORM.PLACEHOLDERS.LINKEDIN_ID' | translate"
											formControlName="linkedInId"
											fullWidth
											nbInput
										/>
									</div>
								</div>
							</div>
						</div>
					</nb-accordion-item-body>
				</nb-accordion-item>
			</nb-accordion>
		</div>
		<div class="actions">
			<button nbButton status="success">
				{{ 'BUTTONS.SAVE' | translate }}
			</button>
		</div>
	</section>
</form>
