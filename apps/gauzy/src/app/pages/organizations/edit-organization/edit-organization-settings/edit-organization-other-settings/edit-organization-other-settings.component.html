<form class="main-form" [formGroup]="form">
	<aside class="aside-nav">
		<h4 class="settings-section-header">
			{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS_SECTION' | translate }}
		</h4>
		<ul>
			<span (click)="general.toggle()">
				<li [ngClass]="{ active: general?.expanded }">
					{{ 'ORGANIZATIONS_PAGE.EDIT.GENERAL_SETTINGS' | translate }}
				</li>
			</span>
			<span (click)="design.toggle()">
				<li [ngClass]="{ active: design?.expanded }">
					{{ 'ORGANIZATIONS_PAGE.EDIT.DESIGN' | translate }}
				</li>
			</span>
			<span (click)="accounting.toggle()">
				<li [ngClass]="{ active: accounting?.expanded }">
					{{ 'ORGANIZATIONS_PAGE.EDIT.ACCOUNTING' | translate }}
				</li>
			</span>
			<span (click)="bonus.toggle()">
				<li [ngClass]="{ active: bonus?.expanded }">
					{{ 'ORGANIZATIONS_PAGE.EDIT.BONUS' | translate }}
				</li>
			</span>
			<span (click)="invites.toggle()">
				<li [ngClass]="{ active: invites?.expanded }">
					{{ 'ORGANIZATIONS_PAGE.EDIT.INVITE' | translate }}
				</li>
			</span>
			<span (click)="dateLimit.toggle()">
				<li [ngClass]="{ active: dateLimit?.expanded }">
					{{ 'ORGANIZATIONS_PAGE.EDIT.DATE_LIMIT' | translate }}
				</li>
			</span>
			<span (click)="timer.toggle()">
				<li [ngClass]="{ active: timer?.expanded }">
					{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TIMER_SETTINGS' | translate }}
				</li>
			</span>
			<span (click)="taskSetting.toggle()">
				<li [ngClass]="{ active: taskSetting?.expanded }">
					{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TASK_SETTING' | translate }}
				</li>
			</span>
			<span (click)="integrations.toggle()">
				<li [ngClass]="{ active: integrations?.expanded }">
					{{ 'ORGANIZATIONS_PAGE.EDIT.INTEGRATIONS' | translate }}
				</li>
			</span>
		</ul>
	</aside>
	<section class="fields-section">
		<div class="actions">
			<button
				[disabled]="form.invalid"
				(throttledClick)="updateOrganizationSettings()"
				debounceClick
				nbButton
				status="success"
			>
				{{ 'BUTTONS.SAVE' | translate }}
			</button>
		</div>
		<div class="accordion-section">
			<nb-accordion #accordion>
				<nb-accordion-item #general [expanded]="true">
					<nb-accordion-item-header>
						{{ 'ORGANIZATIONS_PAGE.EDIT.GENERAL_SETTINGS' | translate }}
					</nb-accordion-item-header>
					<nb-accordion-item-body #generalBody [hidden]="generalBody?.state === 'collapsed'">
						<div class="fields">
							<div class="row">
								<div class="col-6">
									<ga-timezone-selector formControlName="timeZone"></ga-timezone-selector>
								</div>
								<div class="col-6">
									<div class="form-group">
										<label class="label" for="startWeekOnSelect">{{
											'FORM.LABELS.START_WEEK_ON' | translate
										}}</label>
										<nb-select
											class="d-block"
											size="medium"
											formControlName="startWeekOn"
											id="startWeekOnSelect"
											[placeholder]="'FORM.PLACEHOLDERS.START_WEEK_ON' | translate"
											fullWidth="true"
										>
											<nb-option *ngFor="let weekday of weekdays" [value]="weekday">
												{{ 'SM_TABLE.' + weekday | translate }}
											</nb-option>
										</nb-select>
									</div>
								</div>
								<div class="form-group col-6 date-picker-form">
									<label class="label">{{ 'FORM.LABELS.START_TIME' | translate }}</label>
									<ga-timer-picker fullWidth formControlName="defaultStartTime"></ga-timer-picker>
								</div>
								<div class="form-group col-6 date-picker-form">
									<label class="label">{{ 'FORM.LABELS.END_TIME' | translate }}</label>
									<ga-timer-picker fullWidth formControlName="defaultEndTime"></ga-timer-picker>
								</div>
							</div>
							<div class="row">
								<div class="col-6">
									<div class="form-group">
										<label class="label">{{ 'FORM.LABELS.DATE_TYPE' | translate }}</label>
										<nb-select
											class="d-block"
											size="medium"
											formControlName="defaultValueDateType"
											[placeholder]="'FORM.PLACEHOLDERS.DATE_TYPE' | translate"
											fullWidth
										>
											<nb-option *ngFor="let type of defaultValueDateTypes" [value]="type">
												{{ 'SM_TABLE.' + type | translate }}
											</nb-option>
										</nb-select>
									</div>
								</div>
								<div class="col-6">
									<div class="form-group">
										<label class="label">{{ 'ORGANIZATIONS_PAGE.EDIT.REGIONS' | translate }}</label>
										<nb-select
											class="d-block"
											size="medium"
											[placeholder]="'FORM.PLACEHOLDERS.REGIONS' | translate"
											formControlName="regionCode"
											fullWidth
										>
											<nb-option *ngFor="let code of regionCodes" [value]="code">
												{{ 'SM_TABLE.REGION.' + code | translate }}
											</nb-option>
										</nb-select>
									</div>
								</div>
								<div class="col-6">
									<div class="form-group">
										<label class="label">{{ 'FORM.PLACEHOLDERS.NUMBER_FORMAT' | translate }}</label>
										<nb-select
											class="d-block"
											size="medium"
											[placeholder]="'FORM.PLACEHOLDERS.NUMBER_FORMAT' | translate"
											formControlName="numberFormat"
											fullWidth
										>
											<nb-option *ngFor="let numFormat of numberFormats" [value]="numFormat">
												{{ numberFormatPreview(numFormat) }}
											</nb-option>
										</nb-select>
									</div>
								</div>
								<div class="col-6">
									<div class="form-group">
										<label class="label">
											{{ 'FORM.LABELS.DATE_FORMAT' | translate }}
										</label>
										<nb-select
											class="d-block"
											size="medium"
											formControlName="dateFormat"
											[placeholder]="'FORM.PLACEHOLDERS.CHOOSE_FORMAT' | translate"
											fullWidth
										>
											<nb-option *ngFor="let format of listOfDateFormats" [value]="format">
												{{ dateFormatPreview(format) }}
											</nb-option>
										</nb-select>
									</div>
								</div>
								<div class="col-6">
									<div class="form-group">
										<label class="label">{{ 'FORM.LABELS.TIME_FORMAT' | translate }}</label>
										<ng-select
											[items]="listOfTimeFormats"
											[clearable]="false"
											appendTo="body"
											[searchable]="false"
											formControlName="timeFormat"
											class="time-format-select"
										></ng-select>
									</div>
								</div>
								<div class="col-6">
									<div class="form-group">
										<label class="label">{{ 'FORM.LABELS.DEFAULT' | translate }}</label>
										<button
											nbButton
											status="info"
											size="small"
											ghost
											[nbTooltip]="
												'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ALLOW_TO_SET_DEFAULT_ORGANIZATION'
													| translate
											"
											class="p-0"
										>
											<nb-icon icon="info"></nb-icon>
										</button>
										<nb-select
											class="d-block"
											size="medium"
											[placeholder]="'FORM.PLACEHOLDERS.DEFAULT' | translate"
											formControlName="isDefault"
											fullWidth
										>
											<nb-option
												*ngFor="let item of defaultOrganizationSelection"
												[value]="item.value"
											>
												{{ item?.key }}
											</nb-option>
										</nb-select>
									</div>
								</div>
							</div>
						</div>
					</nb-accordion-item-body>
				</nb-accordion-item>
				<nb-accordion-item #design>
					<nb-accordion-item-header>
						{{ 'ORGANIZATIONS_PAGE.EDIT.DESIGN' | translate }}
					</nb-accordion-item-header>
					<nb-accordion-item-body #designBody [hidden]="designBody?.state === 'collapsed'">
						<div class="fields">
							<div class="row">
								<!-- <div class="col-6 color-pick">
									<div class="form-group">
										<label class="label">
											{{
												'FORM.LABELS.BRAND_COLOR'
													| translate
											}}
										</label>
										<input
											fullWidth
											id="brandColorInput"
											nbInput
											formControlName="brandColor"
											[placeholder]="
												'FORM.PLACEHOLDERS.ADD_COLOR'
													| translate
											"
											[colorPicker]="
												form.get('brandColor').value
											"
											[style.color]="
												backgroundContrast(form.get('brandColor').value)
											"
											[style.background]="
												form.get('brandColor').value+' !important'
											"
											[value]="form.get('brandColor').value"
											(colorPickerChange)="
												form
													.get('brandColor')
													.setValue($event)
											"
										/>
									</div>
								</div> -->
								<div class="select-wrapper">
									<div class="form-group design-select">
										<label class="label">
											{{ 'FORM.LABELS.LOGO_ALIGNMENT' | translate }}
										</label>
										<nb-select
											class="d-block"
											size="medium"
											formControlName="defaultAlignmentType"
											[placeholder]="'FORM.PLACEHOLDERS.ALIGN_LOGO_TO' | translate"
											fullWidth
										>
											<nb-option
												*ngFor="let type of defaultAlignmentTypes"
												[value]="type.toUpperCase()"
											>
												{{ type }}
											</nb-option>
										</nb-select>
									</div>
									<div class="form-group design-select">
										<label class="label">
											{{ 'FORM.PLACEHOLDERS.CURRENCY_POSITION' | translate }}
										</label>
										<nb-select
											class="d-block"
											size="medium"
											formControlName="currencyPosition"
											[placeholder]="'FORM.PLACEHOLDERS.CURRENCY_POSITION' | translate"
											fullWidth
										>
											<nb-option
												*ngFor="let pos of defaultCurrencyPosition"
												[value]="pos.toUpperCase()"
											>
												{{ pos }}
											</nb-option>
										</nb-select>
									</div>
								</div>
							</div>
						</div>
					</nb-accordion-item-body>
				</nb-accordion-item>
				<nb-accordion-item #accounting>
					<nb-accordion-item-header>
						{{ 'ORGANIZATIONS_PAGE.EDIT.ACCOUNTING' | translate }}
					</nb-accordion-item-header>
					<nb-accordion-item-body #accountingBody [hidden]="accountingBody?.state === 'collapsed'">
						<div class="fields">
							<div class="row fiscal-years">
								<div class="year-pick">
									<div class="form-group">
										<label class="label">
											{{ 'FORM.LABELS.FISCAL_YEAR_START_DATE' | translate }}
										</label>
										<input
											fullWidth
											id="fiscalStartDate"
											type="date"
											nbInput
											value="2017-06-01"
											formControlName="fiscalStartDate"
										/>
									</div>
								</div>
								<div class="year-pick">
									<div class="form-group">
										<label class="label">
											{{ 'FORM.LABELS.FISCAL_YEAR_END_DATE' | translate }}</label
										>
										<input
											fullWidth
											type="date"
											id="fiscalEndDate"
											nbInput
											formControlName="fiscalEndDate"
										/>
									</div>
								</div>
							</div>
							<div class="row toggles">
								<div class="form-group toggle-switch">
									<label class="label">
										{{ 'FORM.LABELS.TAX_AND_DISCOUNT_INVOICE_ITEMS_SEPARATELY' | translate }}
									</label>
									<nb-toggle
										class="d-block"
										formControlName="separateInvoiceItemTaxAndDiscount"
										status="primary"
										labelPosition="start"
									>
										{{
											'FORM.LABELS.ALLOW_TAXING_AND_DISCOUNTING_OF_INVOICE_ITEMS_SEPARATELY'
												| translate
										}}
									</nb-toggle>
								</div>
								<div class="form-group toggle-switch">
									<label class="label">
										{{ 'FORM.LABELS.DISCOUNT_AFTER_TAX' | translate }}
									</label>
									<nb-toggle
										class="d-block"
										formControlName="discountAfterTax"
										status="primary"
										labelPosition="start"
									>
										{{
											'FORM.LABELS.APPLY_DISCOUNT_AFTER_TAX_FOR_INVOICES_AND_ESTIMATES'
												| translate
										}}
									</nb-toggle>
								</div>
								<div class="form-group toggle-switch">
									<label class="label">
										{{ 'FORM.LABELS.CONVERT_ESTIMATES' | translate }}
									</label>
									<nb-toggle
										class="d-block"
										formControlName="convertAcceptedEstimates"
										status="primary"
										labelPosition="start"
									>
										{{ 'FORM.LABELS.ALLOW_CONVERTING' | translate }}
									</nb-toggle>
								</div>
							</div>
							<div class="row half-width-inputs">
								<div class="col-6">
									<div class="form-group">
										<label class="label">
											{{ 'FORM.LABELS.DEFAULT_DAYS' | translate }}
										</label>
										<input
											[placeholder]="'FORM.PLACEHOLDERS.DAYS_UNTIL_DUE' | translate"
											formControlName="daysUntilDue"
											fullWidth
											type="number"
											nbInput
										/>
									</div>
								</div>
								<div *ngIf="invoiceTemplates" class="col-6">
									<label class="label">
										{{ 'FORM.LABELS.DEFAULT_INVOICE_TEMPLATE' | translate }}
									</label>
									<nb-select
										size="medium"
										[placeholder]="'FORM.PLACEHOLDERS.INVOICE_TEMPLATE' | translate"
										fullWidth
										(selectedChange)="selectTemplate($event)"
										formControlName="invoiceTemplate"
									>
										<nb-option *ngFor="let template of invoiceTemplates" [value]="template.id">
											{{
												'ACCOUNTING_TEMPLATES_PAGE.TEMPLATE_NAMES.' + template.name | translate
											}}
										</nb-option>
									</nb-select>
								</div>
							</div>
							<div class="row half-width-inputs">
								<div class="col-6">
									<label class="label">
										{{ 'FORM.LABELS.DEFAULT_ESTIMATE_TEMPLATE' | translate }}
									</label>
									<nb-select
										size="medium"
										[placeholder]="'FORM.PLACEHOLDERS.ESTIMATE_TEMPLATE' | translate"
										formControlName="estimateTemplate"
										fullWidth
										(selectedChange)="selectTemplate($event)"
									>
										<nb-option [value]="template.id" *ngFor="let template of estimateTemplates">
											{{
												'ACCOUNTING_TEMPLATES_PAGE.TEMPLATE_NAMES.' + template.name | translate
											}}
										</nb-option>
									</nb-select>
								</div>
								<div class="col-6">
									<label class="label">
										{{ 'FORM.LABELS.DEFAULT_RECEIPT_TEMPLATE' | translate }}
									</label>
									<nb-select
										size="medium"
										[placeholder]="'FORM.PLACEHOLDERS.RECEIPT_TEMPLATE' | translate"
										formControlName="receiptTemplate"
										fullWidth
										(selectedChange)="selectTemplate($event)"
									>
										<nb-option [value]="template.id" *ngFor="let template of receiptTemplates">
											{{
												'ACCOUNTING_TEMPLATES_PAGE.TEMPLATE_NAMES.' + template.name | translate
											}}
										</nb-option>
									</nb-select>
								</div>
							</div>
						</div>
					</nb-accordion-item-body>
				</nb-accordion-item>
				<nb-accordion-item #bonus>
					<nb-accordion-item-header>
						{{ 'ORGANIZATIONS_PAGE.EDIT.BONUS' | translate }}
					</nb-accordion-item-header>
					<nb-accordion-item-body #bonusBody [hidden]="bonusBody?.state === 'collapsed'">
						<div class="fields">
							<div class="row">
								<div class="col-6">
									<div class="form-group">
										<label class="label">
											{{ 'FORM.LABELS.TYPE_OF_BONUS' | translate }}
										</label>
										<nb-select
											class="d-block"
											size="medium"
											formControlName="bonusType"
											[placeholder]="'FORM.PLACEHOLDERS.TYPE_OF_BONUS' | translate"
											fullWidth
										>
											<nb-option [value]="null">
												{{ 'SM_TABLE.NONE' | translate }}
											</nb-option>
											<nb-option *ngFor="let type of defaultBonusTypes" [value]="type">
												{{ 'SM_TABLE.' + type | translate }}
											</nb-option>
										</nb-select>
									</div>
								</div>
								<div class="col-6">
									<div class="form-group">
										<label class="label">
											{{ 'FORM.LABELS.BONUS_PERCENTAGE' | translate }}
										</label>
										<input
											nbInput
											type="number"
											[min]="0"
											formControlName="bonusPercentage"
											[placeholder]="'FORM.PLACEHOLDERS.BONUS_PERCENTAGE' | translate"
											fullWidth
											class="d-block"
											[ngClass]="{
												'status-danger': form.get('bonusPercentage').invalid
											}"
										/>
									</div>
								</div>
							</div>
						</div>
					</nb-accordion-item-body>
				</nb-accordion-item>
				<nb-accordion-item #invites>
					<nb-accordion-item-header>
						{{ 'ORGANIZATIONS_PAGE.EDIT.INVITE' | translate }}
					</nb-accordion-item-header>
					<nb-accordion-item-body #invitesBody [hidden]="invitesBody?.state === 'collapsed'">
						<div class="fields">
							<div class="row">
								<div class="col-6">
									<div class="form-group toggle-switch">
										<label class="label">
											{{ 'FORM.LABELS.ENABLE_DISABLE_INVITES' | translate }}
										</label>
										<nb-toggle
											class="d-block"
											formControlName="invitesAllowed"
											status="primary"
											labelPosition="start"
										>
											{{ 'FORM.LABELS.ALLOW_USER_INVITES' | translate }}
										</nb-toggle>
									</div>
								</div>
								<div class="col-6">
									<div class="form-group">
										<label class="label">
											{{ 'FORM.LABELS.INVITE_EXPIRY_PERIOD' | translate }}
										</label>
										<input
											nbInput
											type="number"
											[min]="0"
											formControlName="inviteExpiryPeriod"
											[placeholder]="'FORM.PLACEHOLDERS.INVITE_EXPIRY_PERIOD' | translate"
											fullWidth
											class="d-block"
											[ngClass]="{
												'status-danger': form.get('inviteExpiryPeriod').invalid
											}"
										/>
									</div>
								</div>
							</div>
						</div>
					</nb-accordion-item-body>
				</nb-accordion-item>
				<nb-accordion-item #dateLimit>
					<nb-accordion-item-header>
						{{ 'ORGANIZATIONS_PAGE.EDIT.DATE_LIMIT' | translate }}
					</nb-accordion-item-header>
					<nb-accordion-item-body #dateLimitBody [hidden]="dateLimitBody?.state === 'collapsed'">
						<div class="fields">
							<div class="row">
								<div class="form-group toggle-switch">
									<label class="label">
										{{ 'FORM.LABELS.ENABLE_DISABLE_FUTURE_DATE' | translate }}
									</label>
									<nb-toggle
										class="d-block"
										formControlName="futureDateAllowed"
										status="primary"
										labelPosition="start"
									>
										{{ 'FORM.LABELS.ALLOW_FUTURE_DATE' | translate }}
									</nb-toggle>
								</div>
							</div>
						</div>
					</nb-accordion-item-body>
				</nb-accordion-item>
				<nb-accordion-item #timer>
					<nb-accordion-item-header>
						{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TIMER_SETTINGS' | translate }}
					</nb-accordion-item-header>
					<nb-accordion-item-body #timerBody [hidden]="timerBody?.state === 'collapsed'">
						<div class="fields time-tracker">
							<div class="row">
								<div class="col-xl-6 col-12 pt-2">
									<div class="form-group invite-toggle">
										<nb-toggle
											class="d-block"
											formControlName="allowManualTime"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ALLOW_MANUAL_TIME' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.ALLOW_MANUAL_TIME_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
								<div class="col-xl-6 col-12 pt-2">
									<div class="form-group invite-toggle">
										<nb-toggle
											class="d-block"
											formControlName="allowModifyTime"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ALLOW_MODIFY_TIME' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.ALLOW_MODIFY_TIME_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
								<div class="col-xl-6 col-12 pt-2">
									<div class="form-group invite-toggle">
										<nb-toggle
											class="d-block"
											formControlName="allowDeleteTime"
											labelPosition="start"
											status="primary"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ALLOW_DELETE_TIME' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.ALLOW_DELETE_TIME_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
								<div class="col-xl-6 col-12 pt-2 form-group invite-toggle">
									<nb-toggle
										class="d-block"
										formControlName="allowScreenshotCapture"
										status="primary"
										labelPosition="start"
									>
										{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ALLOW_SCREEN_CAPTURE' | translate }}
										<button
											[nbTooltip]="
												'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ALLOW_SCREEN_CAPTURE_INFO' | translate
											"
											ghost
											nbButton
											size="small"
											status="info"
											class="p-0"
										>
											<nb-icon icon="info"></nb-icon>
										</button>
									</nb-toggle>
								</div>
							</div>
							<div class="row">
								<div class="col-xl-6 col-12 pt-2">
									<div class="form-group invite-toggle">
										<nb-toggle
											class="d-block"
											formControlName="requireReason"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.REQUIRE_REASON' | translate }}
										</nb-toggle>
									</div>
								</div>
								<div class="col-xl-6 col-12 pt-2">
									<div class="form-group invite-toggle">
										<nb-toggle
											class="d-block"
											formControlName="requireDescription"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.REQUIRE_DESCRIPTION' | translate }}
										</nb-toggle>
									</div>
								</div>
								<div class="col-xl-6 col-12 pt-2">
									<div class="form-group invite-toggle">
										<nb-toggle
											class="d-block"
											formControlName="requireProject"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.REQUIRE_PROJECT' | translate }}
										</nb-toggle>
									</div>
								</div>
								<div class="col-xl-6 col-12 pt-2">
									<div class="form-group invite-toggle">
										<nb-toggle
											class="d-block"
											formControlName="requireTask"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.REQUIRE_TASK' | translate }}
										</nb-toggle>
									</div>
								</div>
								<div class="col-xl-6 col-12 pt-2">
									<div class="form-group invite-toggle">
										<nb-toggle
											class="d-block"
											formControlName="requireClient"
											labelPosition="start"
											status="primary"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.REQUIRE_CLIENT' | translate }}
										</nb-toggle>
									</div>
								</div>
								<div class="col-xl-6 col-12 pt-2 form-group invite-toggle">
									<nb-toggle
										class="d-block"
										formControlName="isRemoveIdleTime"
										status="primary"
										labelPosition="start"
									>
										{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.DELETE_IDLE_TIME' | translate }}
										<button
											[nbTooltip]="
												'ORGANIZATIONS_PAGE.EDIT.SETTINGS.DELETE_IDLE_TIME_INFO' | translate
											"
											ghost
											nbButton
											size="small"
											status="info"
											class="p-0"
										>
											<nb-icon icon="info"></nb-icon>
										</button>
									</nb-toggle>
								</div>
								<div class="col-xl-6 col-12 pt-2">
									<div class="form-group invite-toggle">
										<nb-toggle
											class="d-block"
											formControlName="allowTrackInactivity"
											labelPosition="start"
											status="primary"
											[ngClass]="{ start: isTrackInactivity }"
										>
											<div class="row">
												<div class="col-12">
													{{
														'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ENABLE_DETECTION_INACTIVITY'
															| translate
													}}
													<button
														[nbTooltip]="
															'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.ENABLE_DETECTION_INACTIVITY_INFO'
																| translate
														"
														ghost
														nbButton
														size="small"
														status="info"
														class="p-0"
													>
														<nb-icon icon="info"></nb-icon>
													</button>
												</div>
											</div>
											<div *ngIf="isTrackInactivity" class="row">
												<div class="col-xl-6 col-12">
													<label class="label">
														{{
															'ORGANIZATIONS_PAGE.EDIT.SETTINGS.INACTIVITY_TIME_LIMIT'
																| translate
														}}
													</label>
													<button
														[nbTooltip]="
															'ORGANIZATIONS_PAGE.EDIT.SETTINGS.INACTIVITY_TIME_LIMIT_INFO'
																| translate
														"
														ghost
														nbButton
														size="small"
														status="info"
														class="p-0"
													>
														<nb-icon icon="info"></nb-icon>
													</button>
													<nb-select
														[placeholder]="
															'ORGANIZATIONS_PAGE.EDIT.SETTINGS.INACTIVITY_TIME_LIMIT'
																| translate
														"
														formControlName="inactivityTimeLimit"
														fullWidth
														size="medium"
													>
														<nb-option
															*ngFor="let inactivity of listOfInactivityLimits"
															[value]="inactivity"
														>
															{{ inactivity }}
														</nb-option>
													</nb-select>
												</div>
												<div class="col-xl-6 col-12">
													<label class="label">
														{{
															'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ACTIVITY_PROOF_DURATION'
																| translate
														}}
													</label>
													<button
														[nbTooltip]="
															'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ACTIVITY_PROOF_DURATION_INFO'
																| translate
														"
														ghost
														nbButton
														size="small"
														status="info"
														class="p-0"
													>
														<nb-icon icon="info"></nb-icon>
													</button>
													<nb-select
														[placeholder]="
															'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ACTIVITY_PROOF_DURATION'
																| translate
														"
														formControlName="activityProofDuration"
														fullWidth
														size="medium"
													>
														<nb-option
															*ngFor="let proof of listOfActivityProofDuration"
															[value]="proof"
														>
															{{ proof }}
														</nb-option>
													</nb-select>
												</div>
											</div>
										</nb-toggle>
									</div>
								</div>
								<div class="col-xl-6 col-12 pt-2">
									<div class="form-group invite-toggle">
										<nb-toggle
											[ngClass]="{ start: isEnforced }"
											class="d-block"
											formControlName="enforced"
											labelPosition="start"
											status="primary"
										>
											<div class="row">
												<div class="col-12">
													{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ENFORCED' | translate }}
													<button
														[nbTooltip]="
															'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ENFORCED_INFO' | translate
														"
														class="p-0"
														ghost
														nbButton
														size="small"
														status="info"
													>
														<nb-icon icon="info"></nb-icon>
													</button>
												</div>
											</div>
											<div *ngIf="isEnforced" class="row">
												<div class="col-12 mt-2">
													<nb-toggle
														class="d-block"
														formControlName="randomScreenshot"
														labelPosition="start"
														status="primary"
													>
														{{
															'ORGANIZATIONS_PAGE.EDIT.SETTINGS.RANDOM_SCREENSHOT'
																| translate
														}}
														<button
															[nbTooltip]="
																'ORGANIZATIONS_PAGE.EDIT.SETTINGS.RANDOM_SCREENSHOT_INFO'
																	| translate
															"
															class="p-0"
															ghost
															nbButton
															size="small"
															status="info"
														>
															<nb-icon icon="info"></nb-icon>
														</button>
													</nb-toggle>
												</div>
												<div class="col-12 mt-2">
													<nb-toggle
														class="d-block"
														formControlName="trackOnSleep"
														labelPosition="start"
														status="primary"
													>
														{{
															'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TRACK_ON_SLEEP'
																| translate
														}}
														<button
															[nbTooltip]="
																'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TRACK_ON_SLEEP_INFO'
																	| translate
															"
															class="p-0"
															ghost
															nbButton
															size="small"
															status="info"
														>
															<nb-icon icon="info"></nb-icon>
														</button>
													</nb-toggle>
												</div>
												<div class="col-6 mt-2">
													<label class="label">
														{{
															'ORGANIZATIONS_PAGE.EDIT.SETTINGS.SCREENSHOT_FREQUENCY'
																| translate
														}}
													</label>
													<button
														[nbTooltip]="
															'ORGANIZATIONS_PAGE.EDIT.SETTINGS.SCREENSHOT_FREQUENCY_INFO'
																| translate
														"
														class="p-0"
														ghost
														nbButton
														size="small"
														status="info"
													>
														<nb-icon icon="info"></nb-icon>
													</button>
													<nb-select
														[placeholder]="
															'ORGANIZATIONS_PAGE.EDIT.SETTINGS.INACTIVITY_TIME_LIMIT'
																| translate
														"
														formControlName="screenshotFrequency"
														fullWidth
														size="medium"
													>
														<nb-option
															*ngFor="
																let screenshotFrequency of screenshotFrequencyOptions
															"
															[value]="screenshotFrequency"
														>
															{{ screenshotFrequency }}
														</nb-option>
													</nb-select>
												</div>
											</div>
										</nb-toggle>
									</div>
								</div>
								<!-- Standard Work Hours Per Day -->
								<div class="col-xl-6 col-12 pt-2">
									<div class="col-6">
										<div class="form-group">
											<label class="label">
												{{ 'FORM.LABELS.STANDARD_WORK_HOURS_PER_DAY' | translate }}
												<button
													[nbTooltip]="
														'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.STANDARD_WORK_HOURS_PER_DAY'
															| translate
													"
													class="p-0"
													ghost
													nbButton
													size="small"
													status="info"
												>
													<nb-icon icon="info"></nb-icon>
												</button>
											</label>
											<nb-select
												[placeholder]="
													'FORM.PLACEHOLDERS.STANDARD_WORK_HOURS_PER_DAY' | translate
												"
												formControlName="standardWorkHoursPerDay"
												fullWidth
												size="medium"
											>
												<nb-option
													*ngFor="let hour of standardWorkHoursPerDayOptions"
													[value]="hour"
												>
													{{ hour }}
												</nb-option>
											</nb-select>
										</div>
									</div>
								</div>
								<div class="col-xl-6 col-12 pt-2">
									<div class="form-group invite-toggle">
										<nb-toggle
											class="d-block"
											formControlName="trackKeyboardMouseActivity"
											status="primary"
											labelPosition="start"
										>
											{{
												'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TRACK_KEYBOARD_MOUSE_ACTIVITY'
													| translate
											}}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.TRACK_KEYBOARD_MOUSE_ACTIVITY_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
								<div class="col-xl-6 col-12 pt-2">
									<div class="form-group invite-toggle">
										<nb-toggle
											class="d-block"
											formControlName="trackAllDisplays"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TRACK_ALL_DISPLAYS' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.TRACK_ALL_DISPLAYS_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
							</div>
						</div>
					</nb-accordion-item-body>
				</nb-accordion-item>
				<nb-accordion-item #agent>
					<nb-accordion-item-header>
						{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.AGENT_SETTINGS' | translate }}
					</nb-accordion-item-header>
					<nb-accordion-item-body #agentBody [hidden]="agentBody?.state === 'collapsed'">
						<div class="fields time-tracker">
							<div class="row">
								<div class="col-xl-6 col-12 pt-2">
									<div class="form-group invite-toggle">
										<nb-toggle
											class="d-block"
											formControlName="allowAgentAppExit"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ALLOW_AGENT_APP_EXIT' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.ALLOW_AGENT_APP_EXIT_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
								<div class="col-xl-6 col-12 pt-2">
									<div class="form-group invite-toggle">
										<nb-toggle
											class="d-block"
											formControlName="allowLogoutFromAgentApp"
											status="primary"
											labelPosition="start"
										>
											{{
												'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ALLOW_LOGOUT_FROM_AGENT_APP'
													| translate
											}}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.ALLOW_LOGOUT_FROM_AGENT_APP_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
							</div>
						</div>
					</nb-accordion-item-body>
				</nb-accordion-item>
				<nb-accordion-item #taskSetting [formGroup]="taskSettingForm">
					<nb-accordion-item-header>
						{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TASK_SETTING' | translate }}
					</nb-accordion-item-header>
					<nb-accordion-item-body #taskSettingBody [hidden]="taskSettingBody?.state === 'collapsed'">
						<div class="fields">
							<div class="row">
								<div class="col-6">
									<div class="form-group toggle-switch">
										<nb-toggle
											class="d-block"
											formControlName="isTasksPrivacyEnabled"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TASK_PRIVACY' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.TASK_PRIVACY_ENABLED_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
								<div class="col-6">
									<div class="form-group toggle-switch">
										<nb-toggle
											class="d-block"
											formControlName="isTasksMultipleAssigneesEnabled"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.MULTIPLE_ASSIGNEE' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.TASK_MULTIPLE_ASSIGNEE_ENABLED_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-6">
									<div class="form-group toggle-switch">
										<nb-toggle
											class="d-block"
											formControlName="isTasksManualTimeEnabled"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.MANUAL_TIME' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.TASK_MANUAL_TIME_ENABLED_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
								<div class="col-6">
									<div class="form-group toggle-switch">
										<nb-toggle
											class="d-block"
											formControlName="isTasksGroupEstimationEnabled"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.GROUP_ESTIMATION' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.TASK_GROUP_ESTIMATION_ENABLED_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-6">
									<div class="form-group toggle-switch">
										<nb-toggle
											class="d-block"
											formControlName="isTasksEstimationInHoursEnabled"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ESTIMATION_IN_HOUR' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.TASK_ESTIMATION_IN_HOUR_ENABLED_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
								<div class="col-6">
									<div class="form-group toggle-switch">
										<nb-toggle
											class="d-block"
											formControlName="isTasksEstimationInStoryPointsEnabled"
											status="primary"
											labelPosition="start"
										>
											{{
												'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ESTIMATION_IN_STORY_POINT' | translate
											}}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.TASK_ESTIMATION_IN_STORY_POINT_ENABLED_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-6">
									<div class="form-group toggle-switch">
										<nb-toggle
											class="d-block"
											formControlName="isTasksProofOfCompletionEnabled"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.PROOF_OF_COMPLETION' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.TASK_PROOF_OF_COMPLETION_ENABLED_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
								<div class="col-6">
									<div class="form-group">
										<nb-select
											class="d-block"
											size="medium"
											formControlName="tasksProofOfCompletionType"
											[placeholder]="
												'ORGANIZATIONS_PAGE.EDIT.SETTINGS.PROOF_OF_COMPLETION_TYPE' | translate
											"
											fullWidth
										>
											<nb-option [value]="null">
												{{
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.PROOF_OF_COMPLETION_TYPE_DROPDOWN.NONE'
														| translate
												}}
											</nb-option>
											<nb-option
												*ngFor="let type of defaultTaskProofOfCompletionTypes"
												[value]="type"
											>
												{{
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.PROOF_OF_COMPLETION_TYPE_DROPDOWN.' +
														type | translate
												}}
											</nb-option>
										</nb-select>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-6">
									<div class="form-group toggle-switch">
										<nb-toggle
											class="d-block"
											formControlName="isTasksLinkedEnabled"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.LINKED_ISSUE' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.TASK_LINKED_ISSUE_ENABLED_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
								<div class="col-6">
									<div class="form-group toggle-switch">
										<nb-toggle
											class="d-block"
											formControlName="isTasksCommentsEnabled"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.COMMENT' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.TASK_COMMENTS_ENABLED_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-6">
									<div class="form-group toggle-switch">
										<nb-toggle
											class="d-block"
											formControlName="isTasksHistoryEnabled"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.HISTORY' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.TASK_HISTORY_ENABLED_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
								<div class="col-6">
									<div class="form-group toggle-switch">
										<nb-toggle
											class="d-block"
											formControlName="isTasksAcceptanceCriteriaEnabled"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.ACCEPTANCE_CRITERIA' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.TASK_ACCEPTANCE_CRITERIA_ENABLED_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-6">
									<div class="form-group toggle-switch">
										<nb-toggle
											class="d-block"
											formControlName="isTasksDraftsEnabled"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.DRAFT_ISSUE' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.TASK_DRAFT_ISSUE_ENABLED_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
								<div class="col-6">
									<div class="form-group toggle-switch">
										<nb-toggle
											class="d-block"
											formControlName="isTasksAutoStatusEnabled"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.AUTO_STATUS' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.TASK_AUTO_STATUS_ENABLED_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-6">
									<div class="form-group toggle-switch">
										<nb-toggle
											class="d-block"
											formControlName="isTasksNotifyLeftEnabled"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.NOTIFY_TASK_LEFT' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.TASK_NOTIFY_LEFT_ENABLED_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
								<div class="col-6">
									<div class="form-group">
										<input
											nbInput
											type="number"
											[min]="0"
											formControlName="tasksNotifyLeftPeriodDays"
											[placeholder]="
												'ORGANIZATIONS_PAGE.EDIT.SETTINGS.NOTIFY_TASK_LEFT_PERIOD' | translate
											"
											fullWidth
											class="d-block"
											[ngClass]="{
												'status-danger':
													taskSettingForm.get('tasksNotifyLeftPeriodDays').invalid
											}"
										/>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-6">
									<div class="form-group toggle-switch">
										<nb-toggle
											class="d-block"
											formControlName="isTasksAutoCloseEnabled"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.AUTO_CLOSE_ISSUE' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.TASK_AUTO_CLOSE_ENABLED_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
								<div class="col-6">
									<div class="form-group">
										<input
											nbInput
											type="number"
											[min]="0"
											formControlName="tasksAutoClosePeriodDays"
											[placeholder]="
												'ORGANIZATIONS_PAGE.EDIT.SETTINGS.AUTO_CLOSE_ISSUE_PERIOD' | translate
											"
											fullWidth
											class="d-block"
											[ngClass]="{
												'status-danger': taskSettingForm.get('tasksAutoClosePeriodDays').invalid
											}"
										/>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-6">
									<div class="form-group toggle-switch">
										<nb-toggle
											class="d-block"
											formControlName="isTasksAutoArchiveEnabled"
											status="primary"
											labelPosition="start"
										>
											{{ 'ORGANIZATIONS_PAGE.EDIT.SETTINGS.AUTO_ARCHIVE_ISSUE' | translate }}
											<button
												[nbTooltip]="
													'ORGANIZATIONS_PAGE.EDIT.SETTINGS.TOOLTIP.TASK_AUTO_ARCHIVE_ENABLED_INFO'
														| translate
												"
												ghost
												nbButton
												size="small"
												status="info"
											>
												<nb-icon icon="info"></nb-icon>
											</button>
										</nb-toggle>
									</div>
								</div>
								<div class="col-6">
									<div class="form-group">
										<input
											nbInput
											type="number"
											[min]="0"
											formControlName="tasksAutoArchivePeriodDays"
											[placeholder]="
												'ORGANIZATIONS_PAGE.EDIT.SETTINGS.AUTO_ARCHIVE_ISSUE_PERIOD' | translate
											"
											fullWidth
											class="d-block"
											[ngClass]="{
												'status-danger':
													taskSettingForm.get('tasksAutoArchivePeriodDays').invalid
											}"
										/>
									</div>
								</div>
							</div>
						</div>
					</nb-accordion-item-body>
				</nb-accordion-item>
				<nb-accordion-item #integrations>
					<nb-accordion-item-header>
						{{ 'ORGANIZATIONS_PAGE.EDIT.INTEGRATIONS' | translate }}
					</nb-accordion-item-header>
					<nb-accordion-item-body #integrationsBody [hidden]="integrationsBody?.state === 'collapsed'">
						<div class="fields">
							<div class="row">
								<div class="col-6">
									<div class="form-group">
										<label class="label">
											{{ 'FORM.LABELS.UPWORK_ORGANIZATION_ID' | translate }}
											<button [nbTooltip]="" ghost nbButton size="small" status="info">
												<nb-icon icon="info"></nb-icon>
											</button>
										</label>
										<input
											[placeholder]="'FORM.PLACEHOLDERS.UPWORK_ORGANIZATION_ID' | translate"
											formControlName="upworkOrganizationId"
											fullWidth
											nbInput
										/>
									</div>
								</div>
								<div class="col-6">
									<div class="form-group">
										<label class="label">
											{{ 'FORM.LABELS.UPWORK_ORGANIZATION_NAME' | translate }}
											<button [nbTooltip]="" ghost nbButton size="small" status="info">
												<nb-icon icon="info"></nb-icon>
											</button>
										</label>
										<input
											[placeholder]="'FORM.PLACEHOLDERS.UPWORK_ORGANIZATION_NAME' | translate"
											formControlName="upworkOrganizationName"
											fullWidth
											nbInput
										/>
									</div>
								</div>
							</div>
						</div>
					</nb-accordion-item-body>
				</nb-accordion-item>
			</nb-accordion>
		</div>
	</section>
</form>
