name: 'Snyk'

on:
  push:
    branches: [develop]
  pull_request:
    # The branches below must be a subset of the branches above
    branches: [develop]

concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true

jobs:
  analyze:
    name: Analyze
    runs-on: buildjet-4vcpu-ubuntu-2204

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Run Snyk to check for vulnerabilities
        uses: snyk/actions/node@master
        continue-on-error: true # To make sure that SARIF upload gets called
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --sarif-file-output=snyk.sarif

      - name: Upload result to GitHub Code Scanning
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: snyk.sarif
