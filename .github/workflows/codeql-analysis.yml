name: 'CodeQL Analysis'

on:
  push:
    branches: [test]
  pull_request:
    # The branches below must be a subset of the branches above
    branches: [test]

concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true

jobs:
  analyze:
    name: Analyze
    runs-on: buildjet-4vcpu-ubuntu-2204

    strategy:
      fail-fast: false
      matrix:
        language: ['javascript']

    permissions:
      security-events: write
      actions: read
      contents: read

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}

      - name: Autobuild
        uses: github/codeql-action/autobuild@v3

      #- run: |
      #   make bootstrap
      #   make release

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
